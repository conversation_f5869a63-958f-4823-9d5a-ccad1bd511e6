import commonFetcher, { fetchFile } from "@repo/lib/utils/fetcher";
import * as Sentry from "@sentry/nextjs";
import { getNetworkHeaderParams } from "./device";

const apiHost = process.env.NEXT_PUBLIC_API_HOST;
console.info("STU:API_HOST", apiHost);

const fetcher = async <T>(
  url: string,
  prefix: string = "/study-api",
  init?: RequestInit
) => {
  const isCdnUrl = url.includes("https://") && url.includes("static.");

  if (isCdnUrl) {
    return await fetchFile<T>(url);
  }

  const headerParams = getNetworkHeaderParams() ?? {
    // const headerParams = {
    // DEBUG用
    Authorization: `Bearer ${process.env.NEXT_PUBLIC_TEST_TOKEN}`,
    organizationId: `${process.env.NEXT_PUBLIC_ORGANIZATION_ID}`,
    userTypeId: "1",
  };
  return commonFetcher<T>(`${apiHost}${prefix}${url}`, {
    ...init,
    headers: {
      ...init?.headers,
      ...headerParams,
    },
  }).catch((err) => {
    Sentry.captureException(err, { level: "warning" });
    // return Promise.reject(err);
    // toast.error(err?.message?.message || err?.message || "网络请求失败");
    throw err;
  });
};

export async function get<T>(
  url: string,
  { query, prefix }: { query?: Record<string, string>; prefix?: string }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  const separator = params ? (url.includes("?") ? "&" : "?") : "";
  // const isFullUrl = /^https?:\/\//.test(url);
  // const finalUrl = isFullUrl ? url : `${prefix ?? ApiPrefix}${url}`;
  return await fetcher<T>(
    `${url}${separator}${params?.toString() || ""}`,
    prefix,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

export async function post<T>(
  url: string,
  { arg, prefix }: { arg?: object; prefix?: string }
) {
  // const isFullUrl = /^https?:\/\//.test(url);
  // const finalUrl = isFullUrl ? url : `${prefix ?? ApiPrefix}${url}`;
  return await fetcher<T>(url, prefix, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: arg ? JSON.stringify(arg) : undefined,
  });
}

export async function deleted<T>(
  url: string,
  { query, prefix }: { query?: Record<string, string>; prefix?: string }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  const separator = params ? (url.includes("?") ? "&" : "?") : "";
  // const isFullUrl = /^https?:\/\//.test(url);
  // const finalUrl = isFullUrl ? url : `${prefix ?? ApiPrefix}${url}`;
  return await fetcher<T>(
    `${url}${separator}${params?.toString() || ""}`,
    prefix,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

export default fetcher;
