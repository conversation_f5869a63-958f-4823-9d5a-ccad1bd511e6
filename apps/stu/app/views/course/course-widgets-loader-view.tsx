"use client";

// import ScrollCorner from "@/public/images/corner.svg";
import { DialogView } from "@/app/components/dialog/default-dialog";
import { useCourseWidgetModel } from "@/app/models/course-widget-model";

import { Loading } from "@/app/components/common/loading";
import cornerLottie from "@/public/lottie/corner.json";
import { CourseWidgetSummary } from "@/types/app/course";
import { cn } from "@repo/ui/lib/utils";
import * as Sentry from "@sentry/nextjs";
import Lottie from "lottie-react";
import {
  ComponentProps,
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { ExerciseInCourseView } from "../exercise-in-course";
import { GuideWidgetView } from "../guide/guide-view";
import { InteractiveView } from "../interactive/interactive-view";
import { VideoWidgetView } from "../video/video-view";
import { useCourseViewContext } from "./course-view-context";

const WidgetLoader: FC<
  {
    total: number;
    isActive: boolean;
    isNext: boolean;
    isPrev: boolean;
    summary: CourseWidgetSummary;
  } & ComponentProps<"div">
> = ({ total, isActive, isNext, isPrev, summary }) => {
  const {
    next,
    reportCostTime,
    knowledgeId,
    lessonId,
    nextQuestionParams,
    exerciseCompletedRecord,
    refWidgets,
  } = useCourseViewContext();
  const ref = useRef<HTMLDivElement | null>(null);
  const shouldLoad = useMemo(() => {
    return isActive || isNext || isPrev;
  }, [isActive, isNext, isPrev]);

  const { data, isLoading, error } = useCourseWidgetModel({
    knowledgeId,
    lessonId,
    summary: shouldLoad ? summary : undefined,
    nextQuestionParams,
  });
  const { name, index } = summary;

  useEffect(() => {
    if (ref.current) {
      const arr = [...refWidgets.value];
      arr[index] = ref.current;
      refWidgets.value = arr;
    }
  }, [ref, index, refWidgets, isLoading]);

  useEffect(() => {
    if (error) {
      Sentry.captureException(error, { level: "fatal" });
    }
  }, [error]);

  const handleComplete = useCallback(
    (totalTimeSpent?: number) => {
      if (totalTimeSpent) {
        reportCostTime(totalTimeSpent);
      }

      if (!exerciseCompletedRecord.value?.[data?.index || 0]) {
        const newExerciseCompleted = {
          ...exerciseCompletedRecord.value,
          [data?.index || 0]: true,
        };
        exerciseCompletedRecord.value = newExerciseCompleted;
      }

      console.log("handleComplete", {
        data,
        exerciseCompleted: exerciseCompletedRecord.value,
      });

      next();
    },
    [next, reportCostTime, data?.index, exerciseCompletedRecord]
  );

  const renderWidget = useCallback(() => {
    if (isLoading || !data) {
      return <Loading />;
    }

    if (error) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <p className="text-red-1 text-center text-sm">{error.message}</p>
        </div>
      );
    }

    const { type } = data;
    if (type === "guide") {
      return (
        <GuideWidgetView
          content={data}
          active={isActive}
          totalGuideCount={total}
        />
      );
    }

    if (type === "exercise") {
      console.log("exerciseData", {
        data,
        exerciseCompletedRecord: exerciseCompletedRecord.value,
      });

      // 获取练习数据
      let exerciseData = data;
      if (exerciseCompletedRecord.value?.[data.index]) {
        exerciseData = {
          ...exerciseData,
          data: {
            ...exerciseData.data,
            hasNextQuestion: false,
          },
        };
      }

      return (
        <ExerciseInCourseView
          activeInCourse={isActive}
          widgetIndex={data.index}
          exerciseData={exerciseData}
          onComplete={handleComplete}
        />
      );
    }

    if (type === "interactive") {
      const interactiveData = data;
      return (
        <InteractiveView
          active={isActive}
          index={data.index}
          url={interactiveData.data.url}
          type={interactiveData.data.typeName}
          onReport={(e) => {
            console.log(e);
          }}
        />
      );
    }

    if (type === "video") {
      return (
        <VideoWidgetView
          content={data}
          active={isActive}
          totalGuideCount={total}
        />
      );
    }

    return (
      <div className="course-widget-unsupported">不支持的组件类型: {type}</div>
    );
  }, [error, isLoading, data, total, isActive, handleComplete]);

  return (
    <div
      ref={ref}
      data-name={`widget-${index}-${name}`}
      className={cn(
        "relative h-screen w-full overflow-y-auto overscroll-none bg-[#FAF8F6]"
        // type === "video" && isActive && "bg-black"
      )}
    >
      <div className="h-0.25 invisible" />

      {renderWidget()}
    </div>
  );
};

const LottieCorner: FC<{
  show: boolean;
  className?: string;
  onComplete?: () => void;
}> = ({ show, className, onComplete }) => {
  if (!show) {
    return null;
  }
  return (
    <Lottie
      className={className}
      animationData={cornerLottie}
      autoPlay={true}
      loop={false}
      onComplete={() => {
        onComplete?.();
      }}
    />
  );
};

const CourseWidgetsLoaderView: FC<ComponentProps<"div">> = () => {
  const {
    isVersionChanged,
    isLoading,
    error,
    currentIndex,
    total,
    widgetSummarySequence,
    showAnimation,
    refCourseContainer,
    exit,
  } = useCourseViewContext();

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="text-red-1 text-center text-sm">{error.message}</p>
      </div>
    );
  }

  return (
    <>
      <LottieCorner
        show={showAnimation.value}
        className="z-100 fixed left-0 top-0 w-full bg-transparent"
        onComplete={() => (showAnimation.value = false)}
      />
      <div
        ref={refCourseContainer}
        className="relative h-screen w-full transform-gpu overflow-y-scroll"
      >
        <DialogView
          title="当前课程内容已更新，请退出后重新进入课程"
          open={isVersionChanged.value}
          buttons={[
            {
              text: "确定",
              onClick: exit,
              color: "red",
            },
          ]}
        ></DialogView>
        {widgetSummarySequence.map((summary, index) => {
          return (
            <WidgetLoader
              key={`widget-${index}`}
              total={total}
              isActive={index === currentIndex.value}
              isNext={index === currentIndex.value + 1}
              isPrev={index === currentIndex.value - 1}
              summary={summary}
            />
          );
        })}
      </div>
    </>
  );
};

export { CourseWidgetsLoaderView };
