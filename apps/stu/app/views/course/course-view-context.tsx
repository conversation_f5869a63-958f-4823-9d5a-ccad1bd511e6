"use client";
import {
  createContext,
  FC,
  ReactNode,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

import useScreen from "@/app/hooks/use-screen";
import { exitLesson, trackEvent } from "@/app/utils/device";
import {
  CourseSequenceViewmodel,
  useCourseSequenceViewmodel,
} from "@/app/viewmodels/course/course-sequence-vm";
import { Signal, useSignal } from "@preact-signals/safe-react";
import { GuideMode } from "@repo/core/types/data/widget-guide";

type CourseViewContextType = CourseSequenceViewmodel & {
  isProgressBarOpen: boolean;
  setIsProgressBarOpen: (isProgressBarOpen: boolean) => void;
  guideMode: GuideMode;
  setGuideMode: (guideMode: GuideMode) => void;
  showSubtitle: boolean;
  setShowSubtitle: (showSubtitle: boolean) => void;
  playRate: Signal<number>; //文稿播放速率
  videoPlayRate: Signal<number>; //视频播放速率
  exit: () => void;
  screen: { width: number; height: number };
  trackEventWithLessonId: (eventID: string, needWidgetInfo?: boolean) => void;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
  hadShownRedirectComment: Signal<boolean>;
  studySessionId: number;
  studyType: number;
  refCourseContainer: RefObject<HTMLDivElement | null>;
  exerciseCompletedRecord: Signal<{
    [key: number]: boolean;
  }>;
  refWidgets: Signal<HTMLDivElement[]>;
};

const CourseViewContext = createContext<CourseViewContextType>(
  {} as CourseViewContextType
);

const useCourseViewContext = () => useContext(CourseViewContext);

interface CourseViewProviderProps {
  knowledgeId: number;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  children: ReactNode;
  redirectWidgetIndex?: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
  studySessionId: number;
  studyType: number;
}

const CourseViewProvider: FC<CourseViewProviderProps> = ({
  knowledgeId,
  subjectId,
  knowledgeName,
  lessonName,
  children,
  redirectCommentId,
  redirectCommentRootId,
  redirectReferenceId,
  redirectWidgetIndex,
  studySessionId,
  studyType,
}) => {
  const screen = useScreen();
  const refCourseContainer = useRef<HTMLDivElement>(null);
  const refWidgets = useSignal<HTMLDivElement[]>([]);
  // 获取数据及操作数据的方法
  const sequenceVm = useCourseSequenceViewmodel(
    knowledgeId,
    redirectWidgetIndex
  );

  const {
    lessonId,
    currentWidget,
    currentIndex,
    scrollBehavior,
    lessonVersion,
  } = sequenceVm;
  const [isProgressBarOpen, setIsProgressBarOpen] = useState(false);
  const [guideMode, setGuideMode] = useState(GuideMode.follow);
  const [showSubtitle, setShowSubtitle] = useState(true);
  const playRate = useSignal(1);
  const videoPlayRate = useSignal(1);
  const hadShownRedirectComment = useSignal(false);
  const exerciseCompletedRecord = useSignal<{
    [key: number]: boolean;
  }>({});

  const trackEventWithLessonId = useCallback(
    (eventID: string, needWidgetInfo?: boolean) => {
      const map = needWidgetInfo
        ? {
            widgetIndex: currentWidget?.index,
            widgetType: currentWidget?.type,
          }
        : {};
      trackEvent(eventID, {
        lesson_id: lessonId,
        ...map,
      });
    },
    [lessonId, currentWidget]
  );

  const exit = useCallback(() => {
    trackEventWithLessonId("lesson_exit_click", true);
    console.log("exit");
    exitLesson();
  }, [trackEventWithLessonId]);

  useEffect(() => {
    const index = currentIndex.value;
    const ref = refWidgets.value[index];
    // console.log("next::scrollBehavior", ref?.offsetTop, scrollBehavior.value);
    if (!ref) return;
    refCourseContainer.current?.scrollTo({
      top: ref.offsetTop,
      behavior: scrollBehavior.value,
    });
  }, [
    currentIndex.value,
    refCourseContainer,
    refWidgets.value,
    scrollBehavior,
  ]);

  useEffect(() => {
    const handler = () => {
      const index = currentIndex.value;
      refCourseContainer.current?.scrollTo({
        top: window.innerHeight * index,
        behavior: "instant",
      });
    };
    window.addEventListener("resize", handler);
    return () => window.removeEventListener("resize", handler);
  }, [currentIndex]);

  useEffect(() => {
    const handler = () => {
      if (isProgressBarOpen) {
        setIsProgressBarOpen(false);
      }
    };
    document.body.addEventListener("click", handler);

    return () => {
      document.body.removeEventListener("click", handler);
    };
  }, [isProgressBarOpen]);

  const value = {
    ...sequenceVm,
    isProgressBarOpen,
    setIsProgressBarOpen,
    guideMode,
    setGuideMode,
    showSubtitle,
    setShowSubtitle,
    playRate,
    videoPlayRate,
    exit,
    screen,
    trackEventWithLessonId,
    subjectId,
    knowledgeName,
    lessonName,
    redirectCommentId,
    redirectCommentRootId,
    redirectReferenceId,
    hadShownRedirectComment,
    studySessionId,
    studyType,
    refCourseContainer,
    exerciseCompletedRecord,
    refWidgets,
    lessonVersion,
  };
  return (
    <CourseViewContext.Provider value={value}>
      {children}
    </CourseViewContext.Provider>
  );
};

export {
  CourseViewContext,
  CourseViewProvider,
  GuideMode,
  useCourseViewContext,
  type CourseViewContextType,
};
