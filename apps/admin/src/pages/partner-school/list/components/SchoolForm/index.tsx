import { DictTypeEnum } from '@/services/dict';
import { createSchool, updateSchool } from '@/services/partner-school';
import type { CreateSchoolParams } from '@/services/partner-school/types';
import { SchoolItem } from '@/types/school';
import {
  ModalForm,
  ProFormCascader,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { FormInstance, message } from 'antd';
import { useRef } from 'react';

interface SchoolFormProps {
  visible: boolean;
  title: string;
  initialValues?: Partial<SchoolItem>;
  onFinish: (values: CreateSchoolParams) => void;
  onVisibleChange: (visible: boolean) => void;
}

const SchoolForm: React.FC<SchoolFormProps> = ({
  visible,
  title,
  initialValues,
  onFinish,
  onVisibleChange,
}) => {
  const formRef = useRef<FormInstance>(null);
  const { dictCache } = useModel('dictModels');
  const { initialState } = useModel('@@initialState');
  const { allProvinces = [] } = initialState || {};
  console.log('initialState', initialState);
  // const { data: allProvinces } = useSWR<RegionItem[]>('/api/v1/dict/listAllProvinces', fetcher);

  const handleSubmit = async (values: CreateSchoolParams & { region: string[] }) => {
    try {
      let isSuccess = false;
      let schoolRegionID = 0;
      if (values.region && values.region.length > 0) {
        schoolRegionID = parseInt(values.region[values.region.length - 1]);
      }

      const submitData: CreateSchoolParams = {
        ...values,
        schoolRegionID,
        schoolAddress: values.schoolAddress || '',
      };
      if (initialValues) {
        const { status } = await updateSchool({
          schoolId: initialValues.schoolId as number,
          ...submitData,
        });
        if (status === 200) {
          message.success('更新成功');
          isSuccess = true;
        }
      } else {
        const { status } = await createSchool(submitData);
        if (status === 200) {
          message.success('创建成功');
          isSuccess = true;
        }
      }
      if (isSuccess) {
        onFinish?.(submitData);
        // 重置表单
        formRef.current?.resetFields();
      }
      return isSuccess;
    } catch (error: any) {
      message.error(error.message || (initialValues ? '更新失败' : '创建失败'));
      return false;
    }
  };
  return (
    <ModalForm<CreateSchoolParams & { region: string[] }>
      formRef={formRef}
      title={title}
      open={visible}
      width={600}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onVisibleChange(false),
        styles: {
          header: {
            borderBottom: '1px solid #f0f0f0',
            paddingBottom: 16,
            marginBottom: 16,
          },
        },
      }}
      initialValues={{
        ...initialValues,
        region: initialValues?.regionInfos?.map((info) => info.regionDictCode),
      }}
      onFinish={handleSubmit}
      grid={true}
      layout="horizontal"
      labelCol={{ span: 3 }}
      wrapperCol={{ span: 21 }}
    >
      <h3 className="text-sm font-bold mb-6">学校基本信息</h3>

      <ProFormText
        name="schoolName"
        label="名称"
        colProps={{ span: 24 }}
        rules={[{ required: true, message: '请输入学校名称' }]}
        fieldProps={{
          maxLength: 50,
          showCount: true,
        }}
        placeholder="请输入学校名称"
      />

      <ProFormCascader
        name="region"
        label="地区"
        colProps={{ span: 24 }}
        rules={[{ required: true, message: '请选择所在地区' }]}
        fieldProps={{
          options: allProvinces,
          changeOnSelect: true,
          showSearch: true,
          fieldNames: {
            label: 'regionDictName',
            value: 'regionDictCode',
            children: 'subList',
          },
        }}
        placeholder="请选择所在地区"
      />

      <ProFormText
        name="schoolAddress"
        label="地址"
        colProps={{ span: 24 }}
        placeholder="请输入详细地址"
      />

      <ProFormSelect
        name="schoolEduLevel"
        label="学段"
        colProps={{ span: 24 }}
        rules={[{ required: true, message: '请选择学段' }]}
        options={dictCache[DictTypeEnum.PHASE]}
        placeholder="请选择学段"
        disabled={!!initialValues}
      />

      <ProFormDependency name={['schoolEduLevel']}>
        {({ schoolEduLevel }) => {
          const phase = dictCache[DictTypeEnum.PHASE].find((item) => item.value === schoolEduLevel);
          // 根据学段筛选对应的学制选项
          const schoolEduSystemOptions = phase?.children || [];

          if (!schoolEduSystemOptions.length) {
            return null;
          }

          return (
            <ProFormSelect
              name="schoolEduSystem"
              label="学制"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '请选择学制' }]}
              options={schoolEduSystemOptions}
              placeholder="请选择学制"
            />
          );
        }}
      </ProFormDependency>

      <ProFormSelect
        name="schoolNature"
        label="办学性质"
        colProps={{ span: 24 }}
        options={dictCache[DictTypeEnum.SCHOOL_PROPERTY]}
        placeholder="请选择办学性质"
      />

      <ProFormText
        name="schoolTag"
        label="校区标签"
        colProps={{ span: 24 }}
        fieldProps={{
          maxLength: 20,
          showCount: true,
        }}
        placeholder="请输入校区标签，比如主校/分校，南北校区"
      />

      <ProFormSelect
        name="schoolFeature"
        label="办学特色"
        colProps={{ span: 24 }}
        options={dictCache[DictTypeEnum.SCHOOL_FEATURE]}
        placeholder="请选择办学特色"
      />

      <ProFormTextArea
        name="remark"
        label="备注"
        colProps={{ span: 24 }}
        fieldProps={{
          rows: 4,
          maxLength: 200,
          showCount: true,
        }}
        placeholder="请输入备注信息"
      />
    </ModalForm>
  );
};

export default SchoolForm;
