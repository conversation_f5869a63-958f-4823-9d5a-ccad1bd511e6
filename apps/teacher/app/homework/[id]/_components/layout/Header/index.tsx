"use client";

import { <PERSON><PERSON> } from "@/ui/button";
import { useComputed } from "@preact-signals/safe-react";
import { format } from "date-fns";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { useTaskContext } from "../../../_context/task-context";
import { useStudentRemind } from "../../tabs/Report/hooks/useStudentRemind";
import { SettingsDrawer } from "./settings-drawer";

import { PageHeader } from "@/components/PageHeader";
import { useApp } from "@/hooks";
import ProcessIcon from "@/public/icons/ic_report.svg";
import { getStudentDetail } from "@/services/homework";
import { StudentDetailV2 } from "@/types/homeWork";
import { Separator } from "@/ui/separator";
import { toast } from "@/ui/toast";
import { removeStorageAsync } from "@/utils/storage-utils";
import { useMount, useRequest, useUpdateEffect } from "ahooks";
import { OfflineCommunicationDrawer } from "../../../../../../components/common/offline-communication";
import { fetchHomeworkDetail } from "../../../_context/task-context";
import { EvaluationDrawer } from "../../tabs/Report/components/evaluation-drawer";
import { PushReminderDrawer } from "../../tabs/Report/components/push-reminder-drawer";
import {
  StudentRemindDrawer,
  StudentStatus,
} from "../../tabs/Report/components/student-remind-drawer";
import { useAnswerResults } from "../../tabs/Results/store/answers";
import { ThumbsUpButton } from "../../thumbs-up-button";

interface ReportHeaderProps {
  onBack: () => void;
}

export default function ReportHeader({ onBack }: ReportHeaderProps) {
  const { userInfo } = useApp();
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [pushReminderOpen, setPushReminderOpen] = useState(false);
  const [evaluationOpen, setEvaluationOpen] = useState(false);
  const [offlineCommunicationOpen, setOfflineCommunicationOpen] =
    useState(false);
  const [studentRemindOpen, setStudentRemindOpen] = useState(false);
  const [viewValue, setViewValue] = useState("class");

  const router = useRouter();
  const searchParams = useSearchParams();

  // 获取Context数据
  const {
    viewMode,
    classData,
    studentData,
    homeworkData,
    currentCourse,
    taskData,
    updateStudentData,
    setActiveTab,
  } = useTaskContext();
  const { data: answerResults } = useAnswerResults();

  // 获取学生提醒相关方法
  const { useBatchPraise } = useStudentRemind();
  const newStudents = homeworkData.value?.students?.filter(
    (student) => student.studentId === Number(studentData.value.studentId)
  );
  const { run: handleRunPraise } = useBatchPraise(
    [studentData.value] as unknown as StudentDetailV2[],
    () => {
      fetchHomeworkDetail();
      run();
    }
  );

  // 获取当前视图对应的数据
  // console.log("class", classData.value, studentData.value);
  const data = useComputed(() =>
    viewMode.value === "class" ? classData.value : studentData.value
  );

  const publishTime = useComputed(() =>
    format((data.value.publishTime || 0) * 1000, "yyyy.MM.dd HH:mm")
  );
  const deadline = useComputed(() =>
    format((data.value.deadline || 0) * 1000, "yyyy.MM.dd HH:mm")
  );

  // 处理鼓励
  const handlePraise = () => {
    if (viewMode.value === "student" && studentData.value.studentId) {
      // handleBatchPraise([Number(studentData.value.studentId)]);
      // console.log("handlePraise", studentData.value);
      handleRunPraise();
    }
  };

  // 处理去处理按钮点击
  const handleProcessClick = () => {
    setStudentRemindOpen(true);
  };

  // 处理返回导航
  const handleBackNavigation = () => {
    // 检查URL中是否有courseId参数
    const courseId = searchParams.get("courseId");
    const source = searchParams.get("source");
    currentCourse.value = null;
    if (source === "course") {
      router.back();
    } else if (viewMode.value === "student") {
      console.log("[Header] Navigating back from student view to class view");
      // 学生视图下，手动构造班级视图的URL
      const params = new URLSearchParams(window.location.search);
      params.delete("view");
      params.delete("studentId");
      params.delete("studentName");
      setActiveTab("report");
      setViewValue("class");
      // params.delete("tab");
      // 保留其他参数如courseId、assignId等
      if (!courseId) {
        currentCourse.value = null;
      } else if (courseId !== (currentCourse.value as any)?.id) {
        currentCourse.value = { id: courseId, name: "", type: 0 };
      }

      // 使用replace避免在历史记录中添加额外条目
      router.replace(`${window.location.pathname}?${params.toString()}`);
    } else {
      // 班级视图下，根据参数决定返回逻辑
      if (courseId || source) {
        removeStorageAsync("homework_source").catch(console.error);
        router.back();
      } else {
        onBack();
      }
    }
  };
  const { run } = useRequest(
    async () => {
      if (!taskData.value?.id || !studentData.value.studentId) return;
      console.log("Fetching student detail for:", studentData.value.studentId);
      return getStudentDetail(
        taskData.value.id,
        taskData.value.classes[0].assignId,
        Number(studentData.value.studentId),
        userInfo?.currentSchoolID || 0,
        "task_praise"
      );
    },
    {
      manual: true,
      debounceWait: 1000,
      onSuccess: (data) => {
        if (!data) return;
        const newStudentData = {
          id: data.studentId,
          name: data.studentName,
          avatar: data.avatar,
          className: "", // API中暂无此字段
          status:
            data.studentAccuracyRate >=
            (data.classAccuracyRate ||
              homeworkData.value?.detail?.avgAccuracy ||
              0)
              ? "good"
              : ("attention" as StudentStatus),
          praiseCount: data.praiseCount,
          attentionCount: data.attentionCount,
          performance: {
            homeworkAccuracy: parseFloat(
              (data.studentAccuracyRate * 100).toFixed(2)
            ),
            averageAccuracy: parseFloat(
              (
                (data.classAccuracyRate ||
                  homeworkData.value?.detail?.avgAccuracy ||
                  0) * 100
              ).toFixed(2)
            ),
            completionRate: parseFloat(
              (data.studentCompletedProgress * 100).toFixed(2)
            ),
            averageCompletionRate: parseFloat(
              (
                (data.classCompletedProgress ||
                  homeworkData.value?.detail?.avgProgress ||
                  0) * 100
              ).toFixed(2)
            ),
          },
          feedback: {
            description: data.attentionText || "",
            recommendations: data.attentionTextList || [],
          },
          pushDefaultText: data.pushDefaultText || "",
          praiseDefaultText: data.praiseDefaultText || "",
          courseUrl: data.courseUrl || "",
        };
        updateStudentData(newStudentData);
      },
      onError: (err) => {
        console.error("Failed to fetch student detail:", err);
        toast.error("获取学生信息失败", {
          description: "请稍后重试",
        });
        // updateStudentData(null); // Clear data on error
        return;
      },
    }
  );
  useMount(() => {
    setViewValue(viewMode.value);
    if (viewMode.value === "student") {
      run();
    }
  });
  useUpdateEffect(() => {
    setViewValue(viewMode.value);
    if (viewMode.value === "student") {
      run();
    }
  }, [viewMode.value]);
  // console.log(
  //   "answerData",
  //   answerResults.value,
  //   studentData.value,
  //   newStudents,
  //   homeworkData.value
  // );
  return (
    <PageHeader onBack={handleBackNavigation} needBack={true} className="pl-0">
      <div className="flex flex-1 flex-col pl-4">
        {/* 顶部导航栏 - 三部分结构 */}
        <div className="h-17.5 flex items-center justify-between py-3">
          <div className="flex flex-1 items-center gap-4">
            {/* 1. 返回按钮 */}
            {/* <div
            onClick={handleBackNavigation}
            className="flex cursor-pointer items-center gap-1"
          >
            <ReturnIcon className="h-9 w-9" />
          </div> */}

            {/* 2. 标题部分 - 上下结构 */}
            <div className="flex flex-col items-start justify-start">
              <h1 className="text-gray-1 text-left text-[1.125rem] font-semibold leading-[150%]">
                {viewValue === "student" ? (
                  <div className="flex items-center">
                    {studentData.value.studentName}
                    <Separator
                      orientation="vertical"
                      className="bg-line-2 mx-2 !h-4 w-[1px]"
                    />
                    {data.value.title}
                  </div>
                ) : (
                  data.value.title
                )}
              </h1>
              {/* 详细信息区域 - 放在标题下方 */}
              <div className="flex justify-start">
                <div className="text-gray-4 inline-flex items-center gap-2 text-[0.75rem] font-normal leading-[150%]">
                  <div className="flex items-center">
                    <span className="text-gray-4">发布时间:</span>
                    <span className="ml-2 font-normal">
                      {publishTime.value}
                    </span>
                  </div>
                  <Separator
                    orientation="vertical"
                    className="bg-line-2 !h-2 w-[1px]"
                  />

                  <div className="flex items-center">
                    <span className="text-gray-4">截止时间:</span>
                    <span className="ml-2 font-normal">{deadline.value}</span>
                  </div>
                  {viewValue === "student" && (
                    <>
                      <Separator
                        orientation="vertical"
                        className="bg-line-2 !h-2 w-[1px]"
                      />
                      <div className="flex items-center">
                        <span className="text-gray-4">完成进度:</span>
                        <span className="ml-2 font-normal">
                          {studentData.value?.progress
                            ? Math.ceil(studentData.value?.progress * 100)
                            : 0}
                          %
                        </span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 3. 右侧按钮区域 */}
          {viewValue === "class" ? (
            <>
              {/* <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-1 rounded-[1.125rem] border border-[#E9ECF5]"
            onClick={() => setSettingsOpen(true)}
          >
            <span>设置</span>
          </Button> */}
            </>
          ) : (
            <div className="flex items-center gap-3">
              {/* 鼓励按钮 */}
              <ThumbsUpButton
                variant="secondary"
                className={`bg-transparent font-normal ${studentData.value.praiseCount >= 1 ? "text-primary-2 cursor-pointer" : "hover:bg-indigo-50"}`}
                onClick={handlePraise}
                disabled={studentData.value.praiseCount >= 1}
                praiseCount={studentData.value.praiseCount}
              >
                {studentData.value.praiseCount < 1 ? "鼓励一下" : "已鼓励"}
              </ThumbsUpButton>

              {/* 去处理按钮 */}
              <Button
                className="text-0.875rem text-gray-2 flex h-9 cursor-pointer items-center gap-2 rounded-[1.125rem] border border-[#E9ECF5] bg-transparent font-[500] font-normal leading-[150%] hover:bg-indigo-50"
                onClick={handleProcessClick}
              >
                <ProcessIcon className="h-5 w-5" width={20} height={20} />
                发消息
              </Button>
            </div>
          )}
        </div>

        {/* 设置抽屉 */}
        <SettingsDrawer open={settingsOpen} onOpenChange={setSettingsOpen} />

        {/* 学生视图相关抽屉 */}
        {viewValue === "student" && (
          <>
            <PushReminderDrawer
              open={pushReminderOpen}
              onOpenChange={setPushReminderOpen}
              studentId={Number(studentData.value.studentId)}
            />

            <EvaluationDrawer
              open={evaluationOpen}
              onOpenChange={setEvaluationOpen}
              studentId={Number(studentData.value.studentId)}
            />

            <OfflineCommunicationDrawer
              open={offlineCommunicationOpen}
              onOpenChange={setOfflineCommunicationOpen}
            />

            <StudentRemindDrawer
              open={studentRemindOpen}
              onOpenChange={setStudentRemindOpen}
              studentId={Number(studentData.value.studentId)}
              type={studentData.value.studentType || "attention"}
            />
          </>
        )}
      </div>
    </PageHeader>
  );
}
