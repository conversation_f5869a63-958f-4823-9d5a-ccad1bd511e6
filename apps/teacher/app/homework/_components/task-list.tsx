import { EmptyState } from "@/components/common/empty-state";
import { useApp } from "@/hooks";
import { useOptimizedNavigation } from "@/hooks/useOptimizedNavigation";
import { ScrollArea } from "@/ui/scroll-area";
import GiLLoading from "@/ui/spin";
import { umeng } from "@/utils";
import { throttle } from "@/utils/storage-utils";
import { UmengCategory, UmengHomeworkAction } from "@/utils/umeng/index";
import { cn } from "@/utils/utils";
import { useComputed } from "@preact-signals/safe-react";
import { useScrollDetection } from "@repo/core/hooks/useScrollDetection";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useMount, useUnmount, useUpdateEffect } from "ahooks";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  TaskDataWithClassData,
  useTaskContext,
} from "../[id]/_context/task-context";
import { useHomeworkListContext } from "../context";
import { NewTaskData, TaskCardSkeleton, TaskCardV2 } from "./task-card-v2";

/**
 * 任务列表组件
 * 显示任务列表，并使用虚拟化技术优化性能
 * 支持触底自动加载更多数据
 */
export function TaskList() {
  const { setOpen } = useApp();
  const { navigate } = useOptimizedNavigation();
  // const { prefetchOnHover } = usePrefetch();

  const { query, taskList, loading, hasMore, loadMore } =
    useHomeworkListContext();
  const { assignId } = useTaskContext();
  const parentRef = useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = useState(false);
  const loadedItems = useRef(new Set<string>()).current;
  const observerRef = useRef<IntersectionObserver | null>(null);
  const [lansSignal, setLansSignal] = useState<number[]>([]);
  const [computedTaskList, setComputedTaskList] = useState<NewTaskData[]>([]);
  const [rowCount, setRowCount] = useState(0);
  const [rowHeight, setRowHeight] = useState<number[]>([]);
  const [widthScreen, setWidthScreen] = useState(0);

  function getCurrentWidth() {
    const viewportWidth = window.innerWidth;
    setWidthScreen(viewportWidth);
  }

  useMount(() => {
    getCurrentWidth();
    window.addEventListener("resize", getCurrentWidth);
  });

  useUnmount(() => {
    window.removeEventListener("resize", getCurrentWidth);
  });

  const columnCount = useComputed(() => {
    if (typeof window === "undefined") return 3;
    const width = window.innerWidth;
    if (width < 640) return 2;
    if (width < 1024) return 3;
    return 3;
  });

  const formatDate = (startDate: number, endDate: number) => {
    const startTimestamp = startDate * 1000;
    const endTimestamp = endDate ? endDate * 1000 : null;
    const start = format(startTimestamp, "MM/dd HH:mm", { locale: zhCN });
    const end = endTimestamp
      ? ` 至 ${format(endTimestamp, "MM/dd HH:mm", { locale: zhCN })}`
      : "";
    return `${start}${end}`;
  };

  useUpdateEffect(() => {
    const list = [];
    for (let i = 0; i < taskList.value.length; i++) {
      if (list.length === 0) {
        const childTask = {
          id: taskList.value[i].id,
          title: taskList.value[i].title,
          type: taskList.value[i].type,
          typeName: taskList.value[i].typeName,
          date: taskList.value[i].date,
          bgColor: taskList.value[i].bgColor,
          lineColor: taskList.value[i].lineColor,
          subject: taskList.value[i].subject,
          creatorId: taskList.value[i].creatorId,
          startTime: taskList.value[i].startTime,
          endTime: taskList.value[i].endTime,
          taskList: [taskList.value[i]],
        };
        list.push(childTask);
      } else {
        const index = list.findIndex(
          (item) => item.id === taskList.value[i].id
        );
        if (index === -1) {
          const childTask = {
            id: taskList.value[i].id,
            title: taskList.value[i].title,
            type: taskList.value[i].type,
            typeName: taskList.value[i].typeName,
            date: taskList.value[i].date,
            bgColor: taskList.value[i].bgColor,
            lineColor: taskList.value[i].lineColor,
            subject: taskList.value[i].subject,
            creatorId: taskList.value[i].creatorId,
            startTime: taskList.value[i].startTime,
            endTime: taskList.value[i].endTime,
            taskList: [taskList.value[i]],
          };
          list.push(childTask);
        } else {
          if (list[index].startTime > taskList.value[i].startTime) {
            list[index].startTime = taskList.value[i].startTime;
            list[index].date = formatDate(
              taskList.value[i].startTime,
              list[index].endTime
            );
          }
          if (list[index].endTime < taskList.value[i].endTime) {
            list[index].endTime = taskList.value[i].endTime;
            list[index].date = formatDate(
              list[index].startTime,
              taskList.value[i].endTime
            );
          }
          list[index].taskList.push(taskList.value[i]);
        }
      }
    }
    setComputedTaskList([...list]);
  }, [taskList.value]);

  useUpdateEffect(() => {
    let count = 0;
    let temp = 0;
    let lansTemp = 0;
    const lans = [];
    for (let i = 0; i < computedTaskList.length; i++) {
      if (temp === 0) {
        temp += computedTaskList[i].taskList.length;
        if (temp >= columnCount.value) {
          count += 1;
          temp = 0;
          // lansSignal.value.push(1);
          lans.push(1);
        } else {
          count += 1;
          lansTemp = 1;
          if (i === computedTaskList.length - 1 && !hasMore.value) {
            // lansSignal.value.push(lansTemp);
            lans.push(lansTemp);
          }
        }
      } else {
        temp += computedTaskList[i].taskList.length;
        if (temp > columnCount.value) {
          count += 1;
          temp =
            computedTaskList[i].taskList.length >= columnCount.value
              ? 0
              : computedTaskList[i].taskList.length;
          // lansSignal.value.push(lansTemp);
          lans.push(lansTemp);
          lansTemp = 1;
          if (computedTaskList[i].taskList.length >= columnCount.value) {
            lans.push(1);
            lansTemp = 0;
          }
          // lansSignal.value.push(lansTemp);
          if (i === computedTaskList.length - 1 && !hasMore.value) {
            // lansSignal.value.push(lansTemp);
            lans.push(lansTemp);
          }
        } else if (temp === columnCount.value) {
          temp = 0;
          lansTemp++;
          // lansSignal.value.push(lansTemp);
          lans.push(lansTemp);
          lansTemp = 0;
        } else {
          lansTemp++;
          if (i === computedTaskList.length - 1 && !hasMore.value) {
            // lansSignal.value.push(lansTemp);
            lans.push(lansTemp);
          }
        }
      }
    }
    setLansSignal([...lans]);
    setRowCount(count);
  }, [computedTaskList, columnCount.value]);

  useUpdateEffect(() => {
    const heights: number[] = [];
    let temp = 0;
    for (let i = 0; i < computedTaskList.length; i++) {
      if (temp === 0) {
        temp += computedTaskList[i].taskList.length;
        if (temp <= 4) {
          heights.push(237);
        } else {
          heights.push(237 + Math.ceil((temp - 4) / 4) * 117);
        }
        if (temp >= columnCount.value) {
          temp = 0;
        }
      } else {
        temp += computedTaskList[i].taskList.length;
        if (temp > columnCount.value) {
          if (computedTaskList[i].taskList.length <= 4) {
            heights.push(237);
          } else {
            heights.push(
              237 +
                Math.ceil((computedTaskList[i].taskList.length - 4) / 4) * 117
            );
          }
          temp = 0;
        } else if (temp === columnCount.value) {
          temp = 0;
        }
      }
    }
    setRowHeight([...heights]);
  }, [computedTaskList, columnCount.value]);

  // const rowCount = Math.ceil(taskList.value.length / columnCount.value);
  const { sentinels } = useScrollDetection(parentRef, [
    {
      direction: "bottom",
      threshold: 0.1,
      onChange: (isVisible) => {
        if (isVisible && taskList.value.length > 0 && hasMore.value) {
          loadMore();
        }
      },
    },
  ]);

  // 设置 IntersectionObserver
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const taskId = entry.target.getAttribute("data-task-id");
            if (taskId) {
              loadedItems.add(taskId);
            }
          }
        });
      },
      {
        root: parentRef.current,
        rootMargin: "50px 0px", // 提前 50px 开始检测
        threshold: 0.1, // 当元素 10% 进入视口时触发
      }
    );

    return () => {
      observerRef.current?.disconnect();
      loadedItems.clear();
    };
  }, [loadedItems]);

  // 使用优化导航和节流处理点击
  const handleClick = useCallback(
    throttle((task: TaskDataWithClassData) => {
      // DONE: 埋点1 => `homework_list_card_click` 作业列表中点课程卡片
      umeng.trackEvent(
        UmengCategory.HOMEWORK,
        UmengHomeworkAction.CARD_CLICK,
        {}
      );

      // 立即收起侧边栏，提供视觉反馈
      setOpen(false);

      // 同步保存任务数据，确保数据可用
      try {
        localStorage.setItem("homework_currentTask", JSON.stringify(task));

        // 数据保存成功后再导航
        requestAnimationFrame(() => {
          navigate(
            `/homework/${task.id}?tab=report&assignId=${task.classData?.assignId}`,
            {
              debounceMs: 100, // 减少防抖时间，提升响应速度
            }
          );
          assignId.value = task.classData?.assignId.toString();
        });
      } catch (error) {
        console.error("Failed to save task to localStorage:", error);
        // 即使保存失败也尝试导航
        navigate(
          `/homework/${task.id}?tab=report&assignId=${task.classData?.assignId}`
        );
        assignId.value = task.classData?.assignId.toString();
      }
    }, 200), // 200ms 节流
    [navigate, setOpen]
  );

  const virtualizer = useVirtualizer({
    count: rowCount,
    getScrollElement: () => parentRef.current,
    estimateSize: (i) => rowHeight[i], //() => 237,
    overscan: 5,
    onChange: (instance) => {
      setIsScrolling(instance.isScrolling);
    },
  });

  // const columnWidth = `calc((100% - ${(columnCount.value - 1) * 16}px) / ${columnCount.value})`;
  const calcColumnWidth = (len: number) =>
    len >= columnCount.value
      ? "100%"
      : `calc((${len * 100}% - ${(columnCount.value - 1) * 16}px) / ${columnCount.value})`;
  const searchResult = useMemo(() => {
    if (query.value.keyword && !loading.value)
      return (
        <div className="text-gray-1 mb-2 mt-4 px-6 text-[1.25rem] font-semibold leading-[150%]">
          包含&ldquo;
          <span className="text-primary-1">{query.value.keyword}</span>
          &rdquo;搜索结果
        </div>
      );
    return null;
  }, [query.value.keyword, loading.value]);

  // 显示空状态
  if (taskList.value.length === 0 && !loading.value) {
    return (
      <div className="flex h-full flex-col">
        {searchResult}
        <EmptyState />
      </div>
    );
  }
  // console.log(
  //   "computedTaskList",
  //   computedTaskList,
  //   virtualizer.getVirtualItems(),
  //   lansSignal,
  //   rowCount
  // );
  return (
    <ScrollArea className="flex h-full flex-col">
      {searchResult}
      <div
        ref={parentRef}
        className={cn(
          "mt-2 w-full flex-1 overflow-auto px-6",
          query.value.keyword ? "h-[calc(100vh-100px)]" : "h-full"
        )}
      >
        <div
          style={{
            height: `${virtualizer.getTotalSize()}px`,
            width: "100%",
            position: "relative",
          }}
        >
          {virtualizer.getVirtualItems().map((virtualRow) => {
            // const rowStartIndex = virtualRow.index * columnCount.value;
            const subArray = lansSignal.slice(0, virtualRow.index);
            const rowStartIndex = subArray.reduce((acc, curr) => acc + curr, 0);
            return (
              <div
                key={virtualRow.key}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: `${virtualRow.size}px`,
                  transform: `translateY(${virtualRow.start}px)`,
                }}
                className="flex gap-4"
              >
                {Array.from({ length: lansSignal[virtualRow.index] }).map(
                  (_, colIndex) => {
                    // const task = taskList.value[rowStartIndex + colIndex];
                    const newTask = computedTaskList[rowStartIndex + colIndex];
                    if (!newTask) return null;
                    return (
                      <div
                        key={colIndex}
                        className="pb-4"
                        style={{
                          width: calcColumnWidth(newTask.taskList.length),
                        }}
                        ref={(el) => {
                          if (el) {
                            el.setAttribute(
                              "data-task-id",
                              newTask.id.toString()
                            );
                            observerRef.current?.observe(el);
                          }
                        }}
                      >
                        {isScrolling &&
                        !loadedItems.has(newTask.id.toString()) ? (
                          <TaskCardSkeleton />
                        ) : (
                          <TaskCardV2
                            // taskData={task}
                            newTaskData={newTask}
                            // onClick={() => handleClick(task)}
                            onClickClass={handleClick}
                            // {...prefetchOnHover(
                            //   `/homework/${newTask.id}?tab=report&assignId=${task.classData?.assignId}`
                            // )}
                            minCardWidth={
                              widthScreen <= 1200 ? "w-[220px]" : "w-[290px]"
                            }
                            screenWidth={widthScreen}
                          />
                        )}
                      </div>
                    );
                  }
                )}
              </div>
            );
          })}
        </div>
        <div className="">{sentinels.bottom}</div>
        {/* 底部加载状态 */}
        {(loading.value || (!hasMore.value && taskList.value.length > 0)) && (
          <div className="py-4 text-center">
            {loading.value ? (
              <GiLLoading
                loading={true}
                mask={true}
                className="flex h-[calc(100vh-200px)] items-center justify-center"
              />
            ) : !hasMore.value && taskList.value.length > 0 ? (
              <div className="text-gray-400">没有更多数据了</div>
            ) : null}
          </div>
        )}
      </div>
    </ScrollArea>
  );
}
