"use client";
import { DeviceContext } from "@/hooks";
import { getScreenSize } from "@repo/lib/utils/device";
import { useCreation } from "ahooks";
import React from "react";

function DeviceProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  /**
   * 只有 APP 端有值，设备尺寸
   */
  const screenSize = useCreation(() => getScreenSize(), []);

  return <DeviceContext value={{ screenSize }}>{children}</DeviceContext>;
}

export default DeviceProvider;
