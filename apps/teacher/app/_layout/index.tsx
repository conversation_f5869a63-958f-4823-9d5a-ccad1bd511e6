import React from "react";
import AppLayout from "./AppLayout";
import AppSkeleton from "./AppSkeleton";
import AuthProvider from "./AuthProvider";
import DeviceProvider from "./DeviceProvider";
import "./init-app";

export default function AppIndex(props: {
  children: React.ReactNode;
  login: React.ReactNode;
}) {
  return (
    <>
      {/* 全局骨架屏 */}
      <AppSkeleton />

      {/* 设备检测层 */}
      <DeviceProvider>
        {/* 登录状态与权限校验层 */}
        <AuthProvider>
          {/* 布局层 */}
          <AppLayout {...props} />
        </AuthProvider>
      </DeviceProvider>
    </>
  );
}
