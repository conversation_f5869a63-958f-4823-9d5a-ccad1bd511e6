"use client";
import { LOCALSTORAGE_TOKEN_KEY } from "@/configs";
import { LoginContext } from "@/hooks";
import { useAppAuthEmitter } from "@/libs";
import { useMemoizedFn, useMount } from "ahooks";
import to from "await-to-js";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import store from "store2";

function AuthProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();

  const [isLogin, setIsLogin] = useState(
    Boolean(store(LOCALSTORAGE_TOKEN_KEY))
  );

  const logout = useMemoizedFn(() => {
    store.clearAll();
    setIsLogin(false);
    router.replace("/login");
  });

  /**
   * 统一处理认证失败 / 权限不足的重定向 （目前用于监听、处理在 axios 响应拦截器中发送的事件）
   */
  useAppAuthEmitter(logout);

  /**
   * 是否正在检查登录状态
   */
  const [isChecking, setIsChecking] = React.useState(true);

  /**
   * 检查登录状态，进行重定向
   */
  useMount(() => {
    const checkAuthAndRedirect = async () => {
      const pathname = window.location.pathname;
      const isLoginPage = pathname.startsWith("/login");

      if (isLogin && isLoginPage) {
        router.replace("/course");
        return;
      }

      if (!isLogin && !isLoginPage) {
        router.replace("/login");
        return;
      }
    };

    to(checkAuthAndRedirect());
    setIsChecking(false);
  });

  return (
    <LoginContext value={{ isLogin, setIsLogin, logout }}>
      {isChecking ? null : children}
    </LoginContext>
  );
}

export default AuthProvider;
