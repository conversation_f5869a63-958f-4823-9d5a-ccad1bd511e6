"use client";

import {
  LOCALSTORAGE_SIDEBAR_STATE_KEY,
  LOCALSTORAGE_TOKEN_KEY,
  LOCALSTORAGE_USER_INFO_KEY,
  SIDEBAR_WIDTH,
  SIDEBAR_WIDTH_ICON,
} from "@/configs";
import { AppContext, useDevice, type AppContextProps } from "@/hooks";
import { useUserRoles } from "@/hooks/useUserRoles";
import { getUserInfo } from "@/services";
import { UserInfo } from "@/types";
import { TooltipProvider } from "@/ui/tooltip";
import { cn } from "@/utils/utils";
import { useSignal } from "@preact-signals/safe-react";
import { useLocalStorageState, useMount, useRequest } from "ahooks";
import * as React from "react";
import store from "store2";
import AppSidebar from "./app-sidebar";

export default function AppProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { screenSize } = useDevice();
  // 侧边栏开启
  const open = useSignal(store(LOCALSTORAGE_SIDEBAR_STATE_KEY) ?? true);
  /**
   * 平板设备 statusBarHeight
   */
  const statusBarHeight = useSignal(screenSize?.statusBarHeight ?? 0);
  /**
   * 用户信息
   */
  const [userInfo, setUserInfo] = useLocalStorageState<UserInfo | null>(
    LOCALSTORAGE_USER_INFO_KEY,
    {
      defaultValue: null,
    }
  );

  const [primarySubject, setPrimarySubject] = React.useState<{
    subjectId: number;
    subjectName: string;
  } | null>(null);

  const {
    setJobs,
    hasJobTypes,
    hasJobType,
    computePrimarySubject,
    getRoleSummary,
  } = useUserRoles();

  const getUserInfoRequest = useRequest(getUserInfo, {
    manual: true,
    onSuccess: (res) => {
      setUserInfo(res);
      setJobs(res?.teacherJobInfos || []);
      setPrimarySubject(computePrimarySubject(res?.teacherJobInfos || []));
      store(LOCALSTORAGE_USER_INFO_KEY, res);
    },
  });

  useMount(() => {
    if (store(LOCALSTORAGE_TOKEN_KEY)) {
      getUserInfoRequest.run();
    }
  });

  const contextValue = React.useMemo<AppContextProps>(
    () => ({
      statusBarHeight: statusBarHeight.value,
      primarySubject,
      hasJobTypes,
      hasJobType,
      getRoleSummary,
      open,
      setOpen: (value: boolean) => {
        open.value = value;
        store(LOCALSTORAGE_SIDEBAR_STATE_KEY, value);
      },
      userInfo,
      setupAppData: async () => {
        await getUserInfoRequest.runAsync();
      },
      cleanupAppData: () => {
        store.clearAll();
        setUserInfo(null);
        setJobs([]);
        setPrimarySubject(null);
      },
    }),
    [
      statusBarHeight.value,
      primarySubject,
      hasJobTypes,
      hasJobType,
      getRoleSummary,
      open,
      userInfo,
      getUserInfoRequest,
      setUserInfo,
      setJobs,
    ]
  );

  return (
    <AppContext.Provider value={contextValue}>
      <TooltipProvider delayDuration={0}>
        <div
          data-slot="sidebar-wrapper"
          style={
            {
              "--sidebar-width": SIDEBAR_WIDTH,
              "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
            } as React.CSSProperties
          }
          className={cn(
            "group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar h-full"
          )}
        >
          <div
            className="bg-fill-light flex h-full overflow-hidden"
            style={{
              paddingTop: `${statusBarHeight.value}px`,
            }}
          >
            <AppSidebar
              style={{
                paddingTop: statusBarHeight.value,
                minHeight: screenSize?.height,
              }}
            />

            <main className="flex-1 overflow-hidden">{children}</main>
          </div>
        </div>
      </TooltipProvider>
    </AppContext.Provider>
  );
}
