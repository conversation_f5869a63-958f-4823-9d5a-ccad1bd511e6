"use client";
import { TEACHER_APP_LAYOUT_ID } from "@/configs";
import { useDevice, useLogin } from "@/hooks";
import React from "react";
import { match } from "ts-pattern";
import AppProvider from "./AppProvider";
import FeedbackProvider from "./feedback/FeedbackProvider";
import "./init-app";

export default function AppLayout({
  children,
  login,
}: Readonly<{
  children: React.ReactNode;
  login: React.ReactNode;
}>) {
  const { isLogin } = useLogin();
  const { screenSize } = useDevice();

  return (
    <div
      id={TEACHER_APP_LAYOUT_ID}
      className="h-screen select-none overflow-auto"
      style={{ minHeight: screenSize?.height }}
    >
      {match(isLogin)
        // 主界面
        .with(true, () => (
          <FeedbackProvider>
            <AppProvider>{children}</AppProvider>
          </FeedbackProvider>
        ))
        // 登录页
        .otherwise(() => login)}
    </div>
  );
}
