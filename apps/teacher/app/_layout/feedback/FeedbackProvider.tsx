"use client";
import { FEEDBACK_TYPE } from "@/enums";
import { FeedbackContext, FeedbackSubmitData } from "@/hooks";
import { getScreenshotUploadSignature, submitFeedback } from "@/services";
import { toast } from "@/ui/toast";
import { getDeviceInfo as getDeviceInfoFromJSBridge } from "@repo/lib/utils/device";
import { useRequest } from "ahooks";
import to from "await-to-js";
import bowser from "bowser";
import React, { useState } from "react";
import { match } from "ts-pattern";
import FeedbackDialog from "./FeedbackDialog";
import FeedbackSheet from "./FeedbackSheet";

// 扩展 Navigator 接口以支持网络连接 API
const getNetworkType = () => {
  const nav = window.navigator;
  if (!nav || typeof navigator !== "object") {
    return "";
  }
  return (
    (nav.connection || nav.mozConnection || nav.webkitConnection)
      ?.effectiveType ?? ""
  );
};

const getDeviceInfo = () => {
  const info = getDeviceInfoFromJSBridge();
  const network_type = getNetworkType();

  if (info) {
    return {
      model: info.deviceBuildId,
      platform: "安卓",
      version: info.deviceAndroidVersion,
      network_type,
      // 1: 教师端（pad） 2: 教师端（web） 3: 学生端（app） 4: 学生端（web） 5: 管理端
      feedbackSourceClientId: 1,
    };
  }

  const browser = bowser.getParser(window.navigator.userAgent);
  const browserInfo = browser.getBrowser();

  return {
    model: browserInfo.name + " " + browserInfo.version,
    platform: browser.getOS().name!,
    version: browser.getOS().version!,
    network_type,
    feedbackSourceClientId: 2,
  };
};

export default function FeedbackProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);

  const [feedbackPayload, setFeedbackPayload] = useState<{
    feedbackType: FEEDBACK_TYPE;
    [prop: string]: unknown;
  } | null>(null);

  const openFeedback = (payload: {
    feedbackType: FEEDBACK_TYPE;
    [prop: string]: unknown;
  }) => {
    setOpen(true);
    setFeedbackPayload(payload);
  };

  const closeFeedback = () => {
    setOpen(false);
    setFeedbackPayload(null);
  };

  const uploadImages = async ({
    images,
    feedbackType,
  }: {
    images: File[];
    feedbackType: FEEDBACK_TYPE;
    feedbackSubType?: string;
  }) => {
    if (!images.length) return [];

    return Promise.all(
      images.map(async (file) => {
        const [err, data] = await to(
          getScreenshotUploadSignature({
            feedbackType,
            fileName: file.name,
          })
        );

        if (err) {
          return {
            url: "",
            error: "上传失败",
          };
        }

        const [err2, res] = await to(
          fetch(data.presignedURL, {
            method: "PUT",
            body: file, // 直接上传整个文件
          })
        );

        if (err2 || !res.ok) {
          return {
            url: "",
            error: "上传失败",
          };
        }

        return {
          url: data.fileUrl,
          error: "",
        };
      })
    );
  };

  // 提交反馈
  const submitRequest = useRequest(
    async (data: FeedbackSubmitData) => {
      const { images, ...rest } = data;

      // 1. 上传图片
      const imageUrls = await uploadImages({
        images,
        feedbackType: data.feedbackType,
      });

      for (let i = 0; i < imageUrls.length; i++) {
        const url = imageUrls[i].url;

        if (!url) {
          toast.error("上传图片失败，请稍后重试");
          throw new Error("上传图片失败");
        }
      }

      const { feedbackSourceClientId, ...deviceInfo } = getDeviceInfo();

      return submitFeedback({
        ...rest,
        screenshotUrls: imageUrls.map((x) => x.url),
        deviceInfo,
        feedbackSourceClientId,
        ...feedbackPayload,
      });
    },
    {
      manual: true,
    }
  );

  return (
    <FeedbackContext value={{ submitRequest, openFeedback, closeFeedback }}>
      {children}

      {match(feedbackPayload?.feedbackType)
        .with(undefined, () => null)
        .with(FEEDBACK_TYPE.OTHER, () => (
          <FeedbackDialog open={open} setOpen={setOpen} />
        ))
        .otherwise(() => (
          <FeedbackSheet
            feedbackType={feedbackPayload!.feedbackType}
            open={open}
            setOpen={setOpen}
          />
        ))}
    </FeedbackContext>
  );
}
