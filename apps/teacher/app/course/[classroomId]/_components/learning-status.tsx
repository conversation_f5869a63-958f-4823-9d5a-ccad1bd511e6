"use client";
import { LearningStatus as ELearningStatus } from "@/enums";
import IcCourseClassroomStatus from "@/public/icons/ic_course_classroom_status.svg";
import IcPause from "@/public/icons/ic_pause.svg";
import { GetStudentLearningStatusResponse, StudentInfo } from "@/types/course";
import { Progress } from "@/ui/progress";
import { Switch } from "@/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import Avatar from "@/ui/tch-avatar";
import { cn } from "@/utils";
import { useMount } from "ahooks";
import { useReducer, useState } from "react";
import CourseSectionTitle from "./course-section-title";

// 一页数量
const PAGE_NUM = 10;

export function LearningStatus({
  setStudent,
  studentData,
}: {
  classroomId: string;
  setStudent: (student: StudentInfo) => void;
  studentData: GetStudentLearningStatusResponse;
}) {
  const [startIndex, setStartIndex] = useReducer((prev) => {
    const startIndex = prev + PAGE_NUM;
    return startIndex >= studentData.length ? 0 : startIndex;
  }, 0);

  useMount(() => {
    const timer = setInterval(() => setStartIndex(), 10000);

    return () => {
      clearInterval(timer);
    };
  });

  const [isAutoPlay, setIsAutoPlay] = useState(true);

  const showStudentData = isAutoPlay
    ? studentData.slice(startIndex, startIndex + PAGE_NUM)
    : studentData;

  return (
    <section>
      <CourseSectionTitle title="全班学习状态" icon={IcCourseClassroomStatus}>
        <span className="flex items-center gap-2">
          <Switch
            className="cursor-pointer"
            checked={isAutoPlay}
            onCheckedChange={(v) => setIsAutoPlay(v)}
          />
          <span className="text-gray-2 text-xs font-medium">自动轮播</span>
        </span>
      </CourseSectionTitle>

      <div className="border-fill-gray-2 overflow-hidden rounded-xl border bg-white p-5">
        <Table className="table-fixed overflow-hidden rounded-lg">
          <TableHeader>
            <TableRow className="bg-fill-gray-2 border-b-0! hover:bg-fill-gray-2 h-11">
              <TableHead className="text-gray-2 w-46 pl-4 font-normal">
                学生
              </TableHead>
              {/* <TableHead className="text-gray-2 w-24.5 text-center font-normal">
                学习能量
              </TableHead> */}
              <TableHead className="text-gray-2 font-normal">
                当前学习内容
              </TableHead>
              <TableHead className="text-gray-2 w-52.5 font-normal">
                完成进度
              </TableHead>
              <TableHead className="text-gray-2 w-30 text-center font-normal">
                正确率
              </TableHead>
            </TableRow>
          </TableHeader>

          <TableBody className={cn("text-gray-1")}>
            {showStudentData.map((student) => (
              <TableRow
                key={student.studentId}
                className="h-13 border-[#E1E5F2] hover:bg-transparent"
              >
                <TableCell className="pl-4">
                  <div className="text-gray-1 flex items-center gap-2.5">
                    <Avatar
                      src={student.avatarUrl}
                      alt="avatar"
                      className="h-6 w-6 flex-none active:opacity-80"
                      onClick={() => {
                        setStudent({
                          studentId: student.studentId,
                          studentName: student.studentName,
                          avatarUrl: student.avatarUrl,
                        });
                      }}
                    />
                    <span className="flex-1 truncate">
                      {student.studentName}
                    </span>
                  </div>
                </TableCell>

                {/* <TableCell className="text-gray-1 text-center">
                  {student.correctAnswers || 0}
                </TableCell> */}

                <TableCell>
                  <div className="flex items-center gap-1">
                    {student.learningStatus === ELearningStatus.Paused && (
                      <IcPause className="h-4 w-4"></IcPause>
                    )}
                    <span
                      className={cn(
                        "flex-1 overflow-hidden text-ellipsis whitespace-nowrap",
                        (student.learningStatus === ELearningStatus.Paused ||
                          !student.isLearningSubject) &&
                          "text-orange-0"
                      )}
                    >
                      {student.curStudyContent}
                    </span>
                  </div>
                </TableCell>

                <TableCell>
                  <div className="text-gray-2 flex items-center justify-center gap-2 text-xs">
                    {student.learningProgress ? (
                      <>
                        <Progress
                          value={student.learningProgress}
                          max={100}
                          className="> div:bg-primary-2 bg-fill-gray-1 rounded-xs h-1"
                        />
                        {Math.floor(student.learningProgress)}%
                      </>
                    ) : (
                      "无进度"
                    )}
                  </div>
                </TableCell>

                <TableCell className="text-gray-1 text-center text-sm">
                  {student.accuracyRate ? (
                    Math.floor(student.accuracyRate) + "%"
                  ) : (
                    <span className="text-gray-2">--</span>
                  )}
                </TableCell>
              </TableRow>
            ))}
            {PAGE_NUM - showStudentData.length > 0 &&
              new Array(PAGE_NUM - showStudentData.length)
                .fill(0)
                .map((_, index) => {
                  return (
                    <TableRow
                      key={index}
                      className="h-13 border-0 hover:bg-transparent"
                    ></TableRow>
                  );
                })}
          </TableBody>
        </Table>
      </div>
    </section>
  );
}
