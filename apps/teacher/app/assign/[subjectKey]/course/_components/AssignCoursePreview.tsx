import AiCoursePreviewLayout from "@/components/business/ResourcePreview/AiCoursePreviewLayout";
import { But<PERSON> } from "@/ui/tch-button";
import { CircleMinusIcon, CirclePlusIcon } from "lucide-react";
import { useAssignCourseContext } from "../store";

export default function AssignCoursePreviewWrapper() {
  console.log("AssignCoursePreviewWrapper BEFORE");
  const {
    aiCoursePreviewData,
    goToSelectTarget,
    selectedAiCourses,
    selectAiCourse,
    unselectAiCourse,
  } = useAssignCourseContext();

  const hasJoined = selectedAiCourses.value.some(
    (item) => item.aiCourse.id === aiCoursePreviewData.value?.aiCourse.id
  );

  const detail = aiCoursePreviewData.value;

  if (!detail) {
    return null;
  }

  const HeaderSuffixNode = hasJoined ? (
    <Button
      type="error"
      size="lg"
      radius="full"
      className="px-4"
      onClick={() => unselectAiCourse(detail)}
    >
      <CircleMinusIcon size={20} className="mr-1.5 size-5" />
      <span>取消加入</span>
    </Button>
  ) : (
    <Button
      type="primary"
      size="lg"
      radius="full"
      className="px-4"
      onClick={() => selectAiCourse(detail)}
    >
      <CirclePlusIcon size={20} className="mr-1.5 size-5" />
      <span>加入任务</span>
    </Button>
  );

  return (
    <AiCoursePreviewLayout
      id={aiCoursePreviewData.value?.bizTreeNodeId?.toString() || ""}
      onBack={goToSelectTarget}
      headerSuffixNode={HeaderSuffixNode}
    />
  );
}
