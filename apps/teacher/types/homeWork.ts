import { TASK_TYPE } from "@/enums";

export interface HomeWorkData {
  tasks: Homework[] | null;
  pageInfo: PageInfo;
}

export interface Course {
  id: number;
  name: string;
}

export interface Resource {
  id: string;
  name: string;
  type: number;
}

export interface Homework {
  courseList?: Course[];
  reports: HomeworkReport[];
  taskId: number;
  taskName: string;
  /**
   * 任务类型，10课程任务，20作业任务，30测试任务，40资源任务
   */
  taskType: TASK_TYPE;
  resources: Resource[];
  subject: number;
  creatorId: number;
}

export interface HomeworkReport {
  assignId: number;
  /**
   * 布置对象信息
   */
  assignObject: AssignObject;
  /**
   * 任务统计数据
   */
  statData: StatData;
}

/**
 * 布置对象信息
 */
export interface AssignObject {
  /**
   * 布置对象类型
   */
  type: number;

  /**
   * 布置对象ID
   */
  id: number;
  /**
   * 布置对象名称
   */
  name: string;
  [property: string]: any;
}

/**
 * 任务统计数据
 */
export interface StatData {
  /**
   * 平均进度，资源类任务
   */
  averageProgress?: number;
  /**
   * 课时数，资源类任务
   */
  classHours?: number;
  /**
   * 完成率，非资源类任务
   */
  completionRate: number;
  /**
   * 正确率，非资源类任务
   */
  correctRate?: number;
  /**
   * 结束时间，时间戳秒
   */
  deadline?: number;
  /**
   * 待关注题目数，非资源类任务
   */
  needAttentionQuestionNum?: number;
  /**
   * 开始时间，时间戳秒
   */
  startTime?: number;
}

export interface PageInfo {
  page: number;
  pageSize: number;
  total: number;
  [property: string]: any;
}

/* -----------------------------------------作业详情----------------------------------------- */
export interface HomeworkDetailsData {
  /**
   * 任务报告数据
   */
  students: StudentBase[];
  detail: HomeworkDetail;
  pageInfo: PageInfo;
}

/**
 * 任务报告数据
 */
export interface HomeworkDetail {
  attentionList: number[];
  attentionStudents: StudentBase[];
  /**
   * 平均正确率，0-1
   */
  avgAccuracy: number;
  /**
   * 班级进度，0-1
   */
  avgProgress: number;
  /**
   * 平均用时，分钟
   */
  avgCostTime: number;
  /**
   * 表扬列表
   */
  praiseList: number[];
  praiseStudents: StudentBase[];
  /**
   * 单个学生的任务报告汇总数据列表
   */
  studentReports: HomeworkDetailReport[];
  /**
   * 资源报告数据列表
   */
  resourceReports: ResourceReport[];
  /**
   * 所属学科
   */
  subjectId: number;
}

/**
 * 资源报告数据
 */
export interface ResourceReport {
  /**
   * 资源ID
   */
  resourceId: string;
  /**
   * 资源类型
   */
  resourceType: number;
  /**
   * 资源名称
   */
  resourceName: string;
  /**
   * 完成率，0-100
   */
  completionRate: number;
  /**
   * 正确率，0-100
   */
  correctRate: number;
  /**
   * 需要关注的题目数量
   */
  needAttentionQuestionNum: number;
  /**
   * 需要关注的学生数量
   */
  needAttentionUserNum: number;
  /**
   * 平均用时，分钟
   */
  averageCostTime: number;

  /**
   * 资源ID，如果是练习，则是练习ID，如果是课程，则是课程的节点ID
   */
  extendedResourceId: string;
}

/**
 * 学生信息基础接口
 */
export interface StudentBase {
  /**
   * 学生 id
   */
  studentId: number;
  /**
   * 学生姓名
   */
  studentName: string;
  /**
   * 头像链接
   */
  avatar?: string;
  /**
   * 推送默认文本
   */
  pushDefaultText?: string;
  /**
   * 课程链接
   */
  courseUrl?: string;
}

export interface HomeworkDetailReport {
  /**
   * 学习能量
   */
  studyScore: number;
  /**
   * 正确率
   */
  accuracyRate: number;
  /**
   * 答题数
   */
  answerNum: number;
  /**
   * 用时，分钟
   */
  costTime: number;
  /**
   * 难度系数
   */
  difficultyDegree: number;
  /**
   * 错题数
   */
  incorrectNum: number;
  /**
   * 完成进度
   */
  progress: number;
  /**
   */
  studentId: number;

  /**
   * 学生标签列表，html 格式
   */
  tags: {
    /**
     * 标签类型
     */
    type: -1 | 0 | 1;
    /**
     * 标签内容
     */
    label: string;
  }[];
}

/**
 * 班级视图答题结果数据
 */
export interface TaskReportAnswersData {
  /**
   * 班级进度，0-1
   */
  progress: number;
  /**
   * 任务布置 id
   */
  assignId: number;
  /**
   * 共性错题数
   */
  commonIncorrectCount: number;
  /**
   * 分页信息
   */
  pageInfo: PageInfo;
  /**
   * 答题信息
   */
  questionAnswers: QuestionAnswer[];
  /**
   * 资源 id，指定查询资源时返回
   */
  resourceId?: string;
  /**
   * 资源类型，指定查询资源时返回
   */
  resourceType?: number;
  /**
   * 任务布置的学生列表
   */
  students: Student[];
  /**
   * 任务 id
   */
  taskId: number;
  /**
   * 任务类型
   */
  taskType?: number;
  /**
   * 题目总数，和过滤条件无关
   */
  totalCount: number;
  [property: string]: any;
}

export interface QuestionAnswer {
  /**
   * 作答人数
   */
  answerCount: number;
  /**
   * 每个学生的答题信息
   */
  answers?: AnswerInfo[];
  /**
   * 平均用时，秒
   */
  avgCostTime: number;
  /**
   * 答错人数
   */
  inCorrectCount: number;
  /**
   * 题目信息
   */
  question: Question;
  /**
   * 资源 id
   */
  resourceId: string;
  /**
   * 资源类型
   */
  resourceType: number;
  [property: string]: any;
}

export interface AnswerInfo {
  /**
   * 学生作答
   */
  answer: string;
  /**
   * 作答时长，秒
   */
  costTime: number;
  /**
   * 是否正确
   */
  isCorrect: boolean;
  /**
   * 学生 id
   */
  studentId: number;
  [property: string]: any;
}

/**
 * 题目信息
 */
export interface Question {
  /**
   * 题目添加顺序，从 1 开始
   */
  index: number;
  /**
   * 题目答案
   */
  questionAnswer: string;
  /**
   * 题目内容
   */
  questionContent: QuestionContent;
  /**
   * 题目难度
   */
  questionDifficult: number;
  /**
   * 题目解答说明
   */
  questionExplanation: string;
  /**
   * 题目 id
   */
  questionId: string;
  /**
   * 题目标签
   */
  questionTags: string[];
  /**
   * 题目主题
   */
  questionTopic: string;
  /**
   * 0全部题型 ，1单选题，2多选题，3填空题，4判断题，5解答题
   */
  questionType: number;
  [property: string]: any;
}

/**
 * 题目内容
 */
export interface QuestionContent {
  /**
   * 选择题的选项
   */
  questionOptionList?: QuestionOptionList[];
  /**
   * 题目序号
   */
  questionOrder: number;
  /**
   * 题目分数
   */
  questionScore: number;
  /**
   * 题干
   */
  questionStem: string;
  [property: string]: any;
}

export interface QuestionOptionList {
  /**
   * 选项 key
   */
  optionKey: string;
  /**
   * 详细内容
   */
  optionVal: string;
  [property: string]: any;
}

export interface Student {
  /**
   * 头像链接
   */
  avatar: string;
  /**
   * 学生 id
   */
  studentId: number;
  /**
   * 学生姓名
   */
  studentName: string;
  [property: string]: any;
}

/**
 * 学生视图答题结果数据，与班级视图结构相同，但answers变为answer
 */
export interface StudentTaskReportAnswersData
  extends Omit<TaskReportAnswersData, "questionAnswers"> {
  /**
   * 答题信息，注意这里是answer而不是answers
   */
  questionAnswers: StudentQuestionAnswer[];
}

export interface StudentQuestionAnswer extends Omit<QuestionAnswer, "answers"> {
  /**
   * 学生作答信息，注意这里是answer而不是answers
   */
  answer?: AnswerInfo;
}

/**
 * 学生行为处理请求参数
 */
export interface StudentBehaviorRequest {
  /**
   * 任务ID
   */
  taskId: number;
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 学生ID列表
   */
  studentIds: number[];
  /**
   * 行为类型：task_praise(鼓励) / task_attention(关注)
   */
  behaviorType: "task_praise" | "task_attention";
  /**
   * 内容
   */
  content?: string;
}

/**
 * 学生详情接口返回类型
 */
export interface StudentDetailResponse {
  studentId: number;
  studentName: string;
  avatar: string;
  praiseCount: number;
  attentionCount: number;
  studentAccuracyRate: number;
  studentCompletedProgress: number;
  classAccuracyRate: number;
  classCompletedProgress: number;
  attentionText: string;
  attentionTextList: string[];
  pushDefaultText: string;
  praiseDefaultText: string;
  courseUrl?: string;
}

/**
 * 学生状态类型
 */
export type StudentStatus = "good" | "attention";

/**
 * 学生详情信息
 */
export interface StudentDetail {
  id: string | number;
  name: string;
  avatar: string;
  className: string;
  status: StudentStatus;
  praiseCount: number;
  attentionCount: number;
  performance: {
    homeworkAccuracy: number;
    averageAccuracy: number;
    completionRate: number;
    averageCompletionRate: number;
  };
  feedback: {
    description: string;
    recommendations: string[];
  };
  pushDefaultText: string;
  courseUrl?: string;
}

export interface StudentDetailV2 {
  studentId: number;
  studentName: string;
  avatar: string;
  attentionCount: number;
  displayMetrics: {
    deadline: number;
    overdueDays: number;
    progress: number;
  };
  priority: number;
  pushDefaultText: string;
  recommendationReason: string;
  recommendationType: string;
  courseUrl: string;
  suggestedActions: string[];
}
