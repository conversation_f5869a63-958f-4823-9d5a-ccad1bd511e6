"use client";
import { TabItem } from "@/app/homework/[id]/_components/layout/TabNav";
import { cn } from "@/utils/utils";
import React, { ReactNode } from "react";

interface BaseTabNavProps<T extends string = string> {
  tabs: TabItem<T>[];
  activeTab: T;
  onTabChange: (tab: T) => void;
  rightContent?: ReactNode;
  className?: string;
  tabClassName?: string;
  // tabWidth?: any;
}

// 渲染纯标签导航
export default function TabNav<T extends string = string>({
  tabs,
  activeTab,
  onTabChange,
  rightContent,
  className = "",
  tabClassName = "",
}: BaseTabNavProps<T>) {
  // 定义标签按钮样式
  const getTabButtonClass = (tab: TabItem<T>) => {
    const isActive = activeTab === tab.id;
    const isDisabled = tab.disabled;

    return `mr-[1.5rem] relative flex-1 items-center justify-center gap-2 pb-1.5 text-center text-base font-medium leading-6 transition-all duration-300 touch-manipulation ${
      isDisabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
    } ${
      isActive
        ? "text-primary-1 hover:text-primary-1"
        : "text-gray-4 hover:text-gray-700"
    }`;
  };

  // 过滤可见标签
  const visibleTabs = tabs.filter((tab) => !tab.hidden);

  // 计算活动标签的索引和引用
  // const activeIndex = visibleTabs.findIndex((tab) => tab.id === activeTab);
  const activeTabRef = React.useRef<HTMLButtonElement>(null);

  // 使用 useEffect 来更新下划线宽度
  const [activePositionInfo, setActivePositionInfo] = React.useState({
    width: 0,
    left: 0,
  });
  React.useEffect(() => {
    if (activeTabRef.current) {
      setActivePositionInfo({
        width: activeTabRef.current.offsetWidth,
        left: activeTabRef.current.offsetLeft,
      });
    }
  }, [activeTab]);

  return (
    <div
      className={`pt-2.25 flex w-full items-center justify-between border-b ${className}`}
    >
      {/* 1. 标签自身 */}
      <div className="relative flex">
        {/* 滑动的下划线 */}
        <div
          className="bg-primary-2 rounded-xs absolute -bottom-px h-0.5 transition-all duration-300 ease-in-out"
          style={{
            width: `${activePositionInfo.width}px`,
            transform: `translateX(${activePositionInfo.left}px)`,
          }}
        />

        {visibleTabs.map((tab) => (
          <button
            key={tab.id}
            ref={tab.id === activeTab ? activeTabRef : null}
            onClick={() => !tab.disabled && onTabChange(tab.id)}
            disabled={tab.disabled}
            className={cn(getTabButtonClass(tab), tabClassName)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* 2. 右侧内容区域 */}
      {rightContent && <div className="flex items-center">{rightContent}</div>}
    </div>
  );
}
