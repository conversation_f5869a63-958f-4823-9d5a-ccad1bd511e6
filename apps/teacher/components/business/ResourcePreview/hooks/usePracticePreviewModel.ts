import { getQuestionSetDetail } from "@/services/preview";
import { QuestionSetGroupItem } from "@/types/assign";
import { minutesToString } from "@/utils";
import { useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { useMemo } from "react";

export default function usePracticePreviewModel(id: string, treeNodeInfo?: { bizTreeNodeName: string; bizTreeNodeId: number }) {
    const { data: practiceData } = useRequest(
        async () => {
            if (!id) {
                return null;
            }
            const res = await getQuestionSetDetail(Number(id));
            if (res) {
                activeGroup.value = res.list[0].questionGroupId || 0;
            }
            return res;
        },
        {
            refreshDeps: [id],
            ready: id !== undefined,
        }
    );



    const activeGroup = useSignal<number>(0);
    const handleActiveGroupChange = (group: QuestionSetGroupItem) => {
        activeGroup.value = group.questionGroupId || 0;
        const dom = document.getElementById(
            `practice-group-${group.questionGroupId}`
        );
        if (dom) {
            dom.scrollIntoView({ behavior: "smooth" });
        }
    };

    const practiceGroupList = useMemo(() => {
        if (!practiceData) {
            return [];
        }
        const { list } = practiceData;
        return Array.isArray(list) ? list : [];
    }, [practiceData]);


    // 看看要不要用外边传入的
    const title = useMemo(() => {
        if (!practiceData) {
            return "";
        }
        const { estimatedTime, list } = practiceData;
        const count = list?.length ?? 0;
        const time = minutesToString(estimatedTime);

        const name = treeNodeInfo ?
            treeNodeInfo.bizTreeNodeName :
            `${list[0]?.questionGroupName}${count > 1 ? '等' : ''}`

        return `巩固练习：${name}（${count}个题组）${estimatedTime ? `/ 约${time}` : ""}`;
    }, [practiceData, treeNodeInfo]);

    return {
        practiceData,
        activeGroup,

        title,
        handleActiveGroupChange,
        practiceGroupList,
    }
}