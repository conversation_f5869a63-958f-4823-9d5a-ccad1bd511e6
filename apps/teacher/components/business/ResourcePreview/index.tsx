import FixedLayout from "@/components/common/FixedLayout";
import { RESOURCE_TYPE } from "@/enums";
import { memo } from "react";
import AiCoursePreviewLayout from "./AiCoursePreviewLayout";
import PracticePreviewLayout from "./PracticePreviewLayout";

type CommonResourcePreviewProps = {
  onBack?: () => void;

  headerSuffixNode?: React.ReactNode;
};

export type AiCourseResourcePreviewProps = {
  resourceId: string;
  resourceType: RESOURCE_TYPE.RESOURCE_TYPE_AI_COURSE;
  subjectKey?: number;
};

export type PracticeResourcePreviewProps = {
  resourceId: string;
  resourceType: RESOURCE_TYPE.RESOURCE_TYPE_PRACTICE;
  subjectKey: number;
};

export type SpecificRecoursePreviewProps =
  | AiCourseResourcePreviewProps
  | PracticeResourcePreviewProps
  | {
      resourceType: RESOURCE_TYPE;
      [key: string]: any;
    };

export type ResourcePreviewProps = CommonResourcePreviewProps &
  SpecificRecoursePreviewProps;

// 资源预览组件
function ResourcePreview({
  resourceId,
  resourceType,
  headerSuffixNode,
  onBack,
  subjectKey,
}: ResourcePreviewProps) {
  if (resourceType === RESOURCE_TYPE.RESOURCE_TYPE_AI_COURSE) {
    return (
      <FixedLayout>
        <AiCoursePreviewLayout
          id={resourceId}
          onBack={onBack}
          headerSuffixNode={headerSuffixNode}
        />
      </FixedLayout>
    );
  }

  if (resourceType === RESOURCE_TYPE.RESOURCE_TYPE_PRACTICE) {
    return (
      <FixedLayout>
        <PracticePreviewLayout
          id={resourceId}
          onBack={onBack}
          subjectKey={subjectKey}
        />
      </FixedLayout>
    );
  }

  onBack?.();

  return null;
}

export default memo(ResourcePreview);
