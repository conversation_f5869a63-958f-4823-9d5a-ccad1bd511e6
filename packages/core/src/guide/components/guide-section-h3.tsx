import { useSignal } from "@preact-signals/safe-react";
import {
  useEffect,
  useLayoutEffect,
  useRef,
} from "@preact-signals/safe-react/react";
import { FC } from "react";
import { useCurrentFrame } from "remotion";
import { useGuideContext } from "../context/guide-context";
import {
  SectionH3Provider,
  SectionH3ProviderProps,
  useSectionH3Context,
} from "../context/section-h3-context";
import { useSketchCanvasRef } from "../context/sketch-canvas-context";
import { HandDrawn } from "./guide-hand-drawn";
import { GuideSectionH4 } from "./guide-section-h4";
import { SketchBoard } from "./sketch-board";

const Draw = () => {
  const { h3Line, sketchProps, partFrames } = useSectionH3Context();
  const { onDrawChange, eraserMode, highlighter } = sketchProps || {};
  const { canvasRef, strokeColor, strokeWidth } = useSketchCanvasRef();
  const frame = useCurrentFrame();
  if (!h3Line) {
    return null;
  }

  const { id, draw } = h3Line;

  if (sketchProps?.mode !== "draw" && draw) {
    return <HandDrawn data={draw} frame={frame} partFrames={partFrames} />;
  }

  if (sketchProps?.mode === "draw") {
    return (
      <SketchBoard
        startFrame={sketchProps?.startFrame || 0}
        outFrame={sketchProps?.outFrame || 0}
        itemId={id ?? ""}
        className="z-100 absolute left-0 top-0 h-full w-full"
        svgData={""}
        // readOnly={isReadOnly}
        onDrawChange={onDrawChange}
        ref={canvasRef}
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        allPaths={draw || []}
        animated={true}
        eraserMode={eraserMode}
        highlighter={highlighter}
      />
    );
  }
};

const SectionH3: FC = () => {
  const { lineIdInRange, commentRef } = useGuideContext();
  const { ref, parts, mergedReferences, lineIds } = useSectionH3Context();
  const size = useSignal({ width: 0, height: 0 });
  const canvasRef = useRef<HTMLCanvasElement>(null);
  useLayoutEffect(() => {
    if (!ref.current || !lineIdInRange || !commentRef) return;
    if (lineIds.includes(lineIdInRange)) {
      commentRef.value = ref.current;
    }
  }, [commentRef, lineIdInRange, lineIds, ref]);

  useEffect(() => {
    if (!ref.current) return;
    const observer = new ResizeObserver(() => {
      if (!ref.current) return;
      size.value = {
        width: ref.current.offsetWidth,
        height: ref.current.offsetHeight,
      };
    });
    size.value = {
      width: ref.current.offsetWidth,
      height: ref.current.offsetHeight,
    };
    observer.observe(ref.current);
    return () => {
      observer.disconnect();
    };
  }, [ref, size]);

  useEffect(() => {
    if (!ref.current) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const doms = ref.current.querySelectorAll<
      HTMLImageElement | HTMLSpanElement
    >("[data-underline=true]");

    const paths: {
      left: number;
      top: number;
      height: { value: number };
      width: number;
      replyed: boolean;
    }[] = [];
    const counts: {
      left: number;
      top: number;
      height: { value: number };
      width: number;
      count: number;
    }[] = [];

    let height = { value: 0 };
    let lastIsLast = false;

    doms.forEach((dom) => {
      let domRects: {
        left: number;
        top: number;
        width: number;
        height: number;
      }[] = Array.from(dom.getClientRects());
      if (dom.classList.contains("math-content-formula")) {
        const lastDisplay = dom.style.display;
        dom.style.display = "inline-block";
        const { height, top: domTop } = dom.getBoundingClientRect();
        domRects = domRects.map(({ left, top, width }) => ({
          left,
          top,
          width,
          height: domTop - top + height,
        }));
        dom.style.display = lastDisplay;
      }
      domRects.forEach((domRect, index) => {
        const last = paths.at(-1);
        if (!last) {
          height = { value: domRect.height };
          paths.push({
            left: domRect.left - rect.left,
            top: domRect.top - rect.top,
            height,
            width: domRect.width,
            replyed: dom.dataset.replyed === "1",
          });
          lastIsLast = false;
        } else {
          if (
            domRect.top - rect.top >
            last.top + last.height.value - domRect.height / 2
          ) {
            // 判断上一行是不是超过了上一行半行
            height = { value: domRect.height };
            paths.push({
              width: domRect.width,
              height,
              left: domRect.left - rect.left,
              top: domRect.top - rect.top,
              replyed: dom.dataset.replyed === "1",
            });
            lastIsLast = false;
          } else {
            if (
              domRect.left - rect.left <= last.left + last.width + 5 &&
              !lastIsLast
            ) {
              last.width += domRect.width;
              last.height.value = Math.max(last.height.value, domRect.height);
              last.replyed = last.replyed || dom.dataset.replyed === "1";
            } else {
              paths.push({
                width: domRect.width,
                height,
                left: domRect.left - rect.left,
                top: domRect.top - rect.top,
                replyed: dom.dataset.replyed === "1",
              });
              lastIsLast = false;
            }
          }
        }
        const count = Number(dom.dataset.lastCount);
        if (count && index === domRects.length - 1) {
          lastIsLast = true;
          counts.push({
            left: domRect.left - rect.left,
            top: domRect.top - rect.top,
            height,
            width: String(count).length * 8,
            count,
          });
        }
      });
    });

    const ctx = canvasRef.current.getContext("2d");
    if (!ctx) return;
    ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    paths.forEach((rect) => {
      ctx.beginPath();
      ctx.moveTo(rect.left * 2, (rect.top + rect.height.value + 2) * 2);
      ctx.lineTo(
        (rect.left + rect.width) * 2,
        (rect.top + rect.height.value + 2) * 2
      );
      ctx.setLineDash([2 * 2, 8 * 2]); // 设置虚线模式：2px 实线，4px 空白
      ctx.lineWidth = 3 * 2;
      ctx.strokeStyle = rect.replyed ? "#FFA666" : "#797776";
      ctx.lineCap = "round";
      ctx.lineJoin = "round";
      ctx.stroke();
    });
  }, [ref, mergedReferences, size.value]);

  return (
    <section
      data-name="section::h3"
      ref={ref}
      className="relative flex flex-col gap-6"
    >
      {parts.map((part, index) => (
        <GuideSectionH4 key={index} data={part} />
      ))}

      <Draw />
      <canvas
        className="z-200 pointer-events-none absolute left-0 top-0 h-full w-full"
        width={size.value.width * 2}
        height={size.value.height * 2}
        ref={canvasRef}
      />
    </section>
  );
};

export const GuideSectionH3: FC<Omit<SectionH3ProviderProps, "children">> = (
  props
) => {
  return (
    <SectionH3Provider {...props}>
      <SectionH3 />
    </SectionH3Provider>
  );
};
