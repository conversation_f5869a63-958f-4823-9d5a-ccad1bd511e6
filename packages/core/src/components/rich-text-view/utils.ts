import katex from "katex";
import "katex/contrib/mhchem";
import {
  QuestionResourceRequestParams,
  QuestionResourceResponse,
} from "./types";

export function addHostToImgSrc(richText: string, host: string): string {
  // 确保host以斜杠结尾
  const normalizedHost = host.endsWith("/") ? host : host + "/";

  // 使用正则表达式匹配并替换img标签的src属性
  return richText.replace(/<img[^>]+src="([^"]*)"[^>]*>/g, (match, src) => {
    // 如果src已经是完整URL或者是data URL，则不处理
    if (
      src.startsWith("http://") ||
      src.startsWith("https://") ||
      src.startsWith("data:")
    ) {
      return match;
    }

    // 移除src可能的前导斜杠
    const cleanSrc = src.startsWith("/") ? src.substring(1) : src;

    // 构建新的img标签
    return match.replace(
      `src="${src}"`,
      `src="${normalizedHost}${cleanSrc}" data-origin-src="${src}"`
    );
  });
}

/**
 * 获取备用图片
 */
export async function fetchQuestionResource(
  params: QuestionResourceRequestParams,
  host: string = ""
): Promise<QuestionResourceResponse> {
  const queryParams = new URLSearchParams(Object.entries(params));
  const url = `${host}/api/v1/resource/get/resource/content?${queryParams.toString()}`;
  const res = await fetch(url);
  const data = await res.json();
  console.log("fetchQuestionResource data", data);
  return data;
}

export const renderFormulasByKatex = (html: string) => {
  return katex.renderToString(html, {
    delimiters: [
      { left: "$$", right: "$$", display: true },
      { left: "$", right: "$", display: false },
      { left: "\\(", right: "\\)", display: false },
      { left: "\\[", right: "\\]", display: true },
      { left: "\\begin{equation}", right: "\\end{equation}", display: true },
      { left: "\\begin{align}", right: "\\end{align}", display: true },
      { left: "\\begin{alignat}", right: "\\end{alignat}", display: true },
      { left: "\\begin{gather}", right: "\\end{gather}", display: true },
      { left: "\\begin{CD}", right: "\\end{CD}", display: true },
    ],
    throwOnError: false,
    output: "html",
    // 优化渲染性能和减少抖动的配置
    strict: false, // 允许一些非标准的LaTeX语法
    trust: false, // 安全考虑
    // 设置最小规则厚度，确保公式渲染一致性
    minRuleThickness: 0.04,
    // 设置最大尺寸限制，防止过大的公式导致布局问题
    maxSize: 10,
    // 设置最大展开次数，防止无限递归
    maxExpand: 1000,
  });
};

export const renderFormulas = (html: string) => {
  // 1. 找到所有公式占位符（\(...\) 或 $$...$$）
  const inlineRegex = /\\\((.*?)\\\)/g; // 匹配 \(...\)
  const dollarInlineRegex = /\$(.*?)\$/g; // 匹配 $...$
  // const blockRegex = /\\\[(.*?[^\\])\\\]/g;   // 匹配 \[...\]
  const blockRegex = /\\\[([\s\S]*?[^\\])\\\]/g; // 匹配 \[...\]
  const dollarBlockRegex = /\$\$(.*?)\$\$/g; // 匹配 $$...$$

  let processedHtml = html;

  // 处理行内公式（\(...\)）
  processedHtml = processedHtml.replace(inlineRegex, (_, formula) => {
    try {
      return katex.renderToString(formula, {
        throwOnError: false,
        displayMode: false,
      });
    } catch (e) {
      console.error("KaTeX 渲染失败:", formula, e);
      return formula; // 渲染失败时回退原始文本
    }
  });
  // 处理行内公式 $...$
  processedHtml = processedHtml.replace(dollarInlineRegex, (_, formula) => {
    try {
      return katex.renderToString(formula, {
        throwOnError: false,
        displayMode: false,
      });
    } catch (e) {
      console.error("KaTeX 渲染失败:", formula, e);
      return formula; // 渲染失败时回退原始文本
    }
  });

  // 处理块级公式 \[...\]
  processedHtml = processedHtml.replace(blockRegex, (_, formula) => {
    try {
      return katex.renderToString(formula, {
        throwOnError: false,
        displayMode: true,
      });
    } catch (e) {
      console.error("KaTeX 渲染失败:", formula, e);
      return formula;
    }
  });
  // 处理块级公式（$$...$$）
  processedHtml = processedHtml.replace(dollarBlockRegex, (_, formula) => {
    try {
      return katex.renderToString(formula, {
        throwOnError: false,
        displayMode: true,
      });
    } catch (e) {
      console.error("KaTeX 渲染失败:", formula, e);
      return formula;
    }
  });

  return processedHtml;
};

export const placeholderImage =
  "data:image/png;base64,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";

export const handleImageError = async (
  event: Event,
  questionId: string,
  resourceApiHost: string = ""
) => {
  if (!questionId) {
    return;
  }

  const imgElement = event.target as HTMLImageElement;
  const src = imgElement.src;
  if (src.includes("data:image/")) {
    return;
  }
  const originSrc = imgElement.dataset.originSrc;
  const fileExt = originSrc?.split(".").at(-1) || "png";
  console.log("originSrc", originSrc, fileExt);
  try {
    // 调用后端API获取新的图片地址
    const newSrc = await fetchQuestionResource(
      {
        questionId: questionId || "",
        resourceFileName: originSrc?.split("/").at(-1) || "",
      },
      resourceApiHost
    ).then((res: QuestionResourceResponse) => {
      console.log("res", res);
      return res.data.resourceData;
    });

    // const newSrc = templateImage;

    if (newSrc) {
      // 替换为新的图片地址
      imgElement.src = "data:image/" + fileExt + ";base64," + newSrc;
    } else {
      // 如果没有获取到新地址，可以设置一个默认的占位图
      imgElement.src = placeholderImage;
    }
  } catch (error) {
    console.error("获取备用图片失败:", error);
    imgElement.src = placeholderImage;
  }
};
